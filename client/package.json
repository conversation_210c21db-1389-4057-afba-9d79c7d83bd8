{"name": "booking-system-client", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint 'src/**/*.{js,vue}' && stylelint 'src/**/*.{css,vue}'", "lint:fix": "eslint --fix 'src/**/*.{js,vue}' && stylelint --fix 'src/**/*.{css,vue}'"}, "dependencies": {"bootstrap": "^5.2.3", "socket.io-client": "^4.6.1", "vue": "^3.2.47", "vue-router": "^4.1.6", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "vite": "^6.3.5"}}