/* eslint-disable */
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { readFileSync } from 'fs';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    __VUE_PROD_DEVTOOLS__: true,
  },
  mode: "development",
  plugins: [vue()],
  preview: {
    port: 5173,
    https: {
      key: readFileSync(resolve(__dirname, '../server/certs/server.key')),
      cert: readFileSync(resolve(__dirname, '../server/certs/server.cert')),
    }
  },
  server: {
    port: 5173,
    https: {
      key: readFileSync(resolve(__dirname, '../server/certs/server.key')),
      cert: readFileSync(resolve(__dirname, '../server/certs/server.cert')),
    }
  },
});
