{"name": "booking-system-server", "version": "1.0.0", "description": "Backend for the booking system", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint 'src/**/*.js' && prettier --check src/", "lint:fix": "eslint --fix 'src/**/*.js' && prettier --write src/"}, "dependencies": {"bcrypt": "^5.1.0", "better-logging": "^5.0.0", "cors": "^2.8.5", "express": "^4.18.2", "express-session": "^1.17.3", "express-socket.io-session": "^1.3.5", "express-validator": "^7.2.1", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "https-redirect": "^1.1.1", "node-schedule": "^2.1.1", "socket.io": "^4.6.1", "sqlite": "^4.2.0", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.1.10"}}