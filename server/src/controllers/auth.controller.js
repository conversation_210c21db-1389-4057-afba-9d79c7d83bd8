/**
 * auth.controller.js - Authentication Controller
 *
 * This controller handles all authentication-related operations for students:
 * - <PERSON><PERSON> (creating a new user session)
 * - Logout (destroying the session)
 * - Checking authentication status
 * - Protecting routes with authentication middleware
 *
 * The authentication system for students is simple:
 * - Students only need to provide a username (no password)
 * - A session is created and associated with the username
 * - The session ID is stored in a cookie on the client
 * - The session is used to identify the user in subsequent requests
 */

import { Router } from "express"; // Express router for defining routes
import model from "../model.js"; // Central data model
import { authValidation } from "../utils/validation.js"; // Input validation rules
import { handleValidationErrors } from "../middleware/security.js"; // Validation error handler

// Create a new router instance for authentication routes
const router = Router();

/**
 * Authentication middleware for protecting routes
 *
 * This middleware:
 * 1. Checks if the request has a valid session with a user
 * 2. If authenticated, updates the user's activity timestamp and allows the request
 * 3. If not authenticated, returns a 401 Unauthorized response
 *
 * Usage example:
 * ```
 * router.get('/protected-route', requireAuth, (req, res) => {
 *   // Only authenticated users can access this route
 * });
 * ```
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {void}
 */
const requireAuth = (req, res, next) => {
  // Get the session ID from the request
  // This is automatically set by express-session middleware
  const { id } = req.session;

  // Look up the user in the model using the session ID
  const user = model.findUserById(id);

  // If no user is found with this session ID, the user is not authenticated
  if (user === undefined) {
    // Return 401 Unauthorized status with error message
    res.status(401).json({ error: "Authentication required" });
    return;
  }

  // Update the user's last activity timestamp
  // This is used for cleaning up inactive users
  user.updateActivity();

  // User is authenticated, proceed to the next middleware or route handler
  next();
};

/**
 * Get the current user's authentication status
 *
 * This endpoint:
 * 1. Checks if the request has a valid session with a user
 * 2. Returns the user's authentication status and details if authenticated
 * 3. Updates the user's activity timestamp
 *
 * Used by the client to check if the user is logged in when the page loads
 * or after a page refresh.
 *
 * @route GET /api/users/me
 * @returns {Object} Authentication status and user details if authenticated
 */
router.get("/users/me", (req, res) => {
  // Get the session ID from the request
  const { id } = req.session;

  // Look up the user in the model using the session ID
  const user = model.findUserById(id);

  // If no user is found, return unauthenticated status (but with 200 OK)
  if (!user) {
    return res.status(200).json({ authenticated: false });
  }

  // Update the user's last activity timestamp
  user.updateActivity();

  // Return authenticated status and user details
  res.status(200).json({
    authenticated: true,
    user: {
      name: user.getName(), // User's display name
      reservedTimeslot: user.getReservedTimeslot(), // ID of timeslot user has reserved (if any)
    },
  });
});

/**
 * Update user activity timestamp
 *
 * This endpoint:
 * 1. Checks if the request has a valid session with a user
 * 2. Updates the user's activity timestamp
 * 3. Returns the user's authentication status
 *
 * Used by the client to update the user's activity timestamp during AJAX polling
 * to prevent automatic logout due to inactivity.
 *
 * @route POST /api/users/activity
 * @returns {Object} Authentication status
 */
router.post("/users/activity", (req, res) => {
  // Get the session ID from the request
  const { id } = req.session;

  // Look up the user in the model using the session ID
  const user = model.findUserById(id);

  // If no user is found, return unauthenticated status
  if (!user) {
    return res.status(200).json({ authenticated: false });
  }

  // Update the user's last activity timestamp
  user.updateActivity();

  // Return authenticated status
  res.status(200).json({ authenticated: true });
});

/**
 * Login as a student
 *
 * This endpoint:
 * 1. Validates the username from the request body using express-validator
 * 2. Creates a new user in the model with the session ID
 * 3. Saves the session to persist the authentication
 *
 * The student login is simple - only a username is required (no password).
 *
 * @route POST /api/login
 * @param {Object} req.body.username - The username to login with
 * @returns {Object} Authentication status and error message if applicable
 */
router.post("/login", [
  authValidation.username(),
  handleValidationErrors
], (req, res) => {
  // Extract username from request body (already validated by middleware)
  const { username } = req.body;

  // Get the session ID from the request
  const { id } = req.session;

  try {
    // Create a new user with the given name and associate it with the session ID
    // This effectively "logs in" the user
    model.createUser(id, username);

    // Save the session to persist the authentication
    req.session.save((err) => {
      if (err) {
        console.error("Error saving session:", err);
        return res
          .status(500)
          .json({ error: "Internal server error", authenticated: false });
      }

      // Log the created user for debugging
      console.debug(`Saved user: ${JSON.stringify(model.findUserById(id))}`);

      // Return successful authentication status
      res.status(200).json({ authenticated: true });
    });
  } catch (error) {
    console.error("Error creating user:", error);
    res
      .status(500)
      .json({ error: "Failed to create user", authenticated: false });
  }
});

/**
 * Logout the current user
 *
 * This endpoint:
 * 1. Cancels any reservations the user has
 * 2. Removes the user from the model
 * 3. Destroys the session
 *
 * @route POST /api/logout
 * @returns {Object} Success status
 */
router.post("/logout", (req, res) => {
  // Get the session ID from the request
  const { id } = req.session;

  // Look up the user in the model using the session ID
  const user = model.findUserById(id);

  // If the user has a reserved timeslot, cancel it
  // This prevents abandoned reservations when users log out
  if (user && user.getReservedTimeslot()) {
    try {
      // Cancel the reservation in the model and database
      model.cancelReservation(user.getReservedTimeslot());

      // Clear the reservation from the user object
      user.clearReservedTimeslot();
    } catch (error) {
      console.error("Error cancelling reservation during logout:", error);
      // Continue with logout even if reservation cancellation fails
    }
  }

  // Remove the user from the model
  if (user) {
    delete model.users[id];
  }

  // Destroy the session to complete the logout
  req.session.destroy((err) => {
    if (err) {
      console.error("Error destroying session:", err);
      return res.status(500).json({ error: "Internal server error" });
    }

    // Return successful logout status
    res.status(200).json({ success: true });
  });
});

// Export the router and requireAuth middleware for use in other modules
// The router is mounted at /api in index.js
// The requireAuth middleware is used to protect routes in other controllers
export default { router, requireAuth };
