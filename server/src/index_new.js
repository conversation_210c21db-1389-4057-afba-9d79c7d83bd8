/**
 * index.js - Main Server Entry Point
 *
 * This file is the central entry point for the lab booking system's backend.
 * It sets up the Express server, configures middleware, initializes the database,
 * establishes routes, and sets up Socket.io for real-time communication.
 *
 * The server handles:
 * - HTTP API requests for authentication, timeslot management, and admin operations
 * - WebSocket connections for real-time updates
 * - Session management for both students and admins
 * - Database initialization and default data creation
 * - Static file serving for the client-side application
 */

// Import required libraries
import betterLogging from "better-logging"; // Enhanced logging capabilities
import express from "express"; // Web server framework
import expressSession from "express-session"; // Session management for Express
import socketIOSession from "express-socket.io-session"; // Share sessions between Express and Socket.io
import { createServer as createHttpServer } from "http"; // HTTP server creation
import { createServer as createHttpsServer } from "https"; // HTTPS server creation
import { Server } from "socket.io"; // WebSocket server for real-time communication
import { fileURLToPath } from "url"; // For ES module path resolution
import { dirname, resolve as resolvePath } from "path"; // Path utilities
import cors from "cors"; // Cross-Origin Resource Sharing
import helmet from "helmet"; // Security headers
import { initDB, getDB } from "./db.js"; // Database initialization and access
import model from "./model.js"; // Central data model for the application

// Import controllers that handle different API endpoints
import auth from "./controllers/auth.controller.js"; // Handles student authentication
import chat from "./controllers/chat.controller.js"; // Handles chat functionality (not core)
import admin from "./controllers/admin.controller.js"; // Handles admin operations
import timeslot from "./controllers/timeslot.controller.js"; // Handles timeslot operations

// Import security middleware and utilities
import { escapeHtml, setSecurityHeaders, handleValidationErrors } from "./middleware/security.js";
import { loadCertificates } from "./utils/certificate.js";

// Define server ports and create Express application
const httpPort = 8080; // HTTP port (for redirecting to HTTPS)
const httpsPort = 8989; // HTTPS port (main server)
const app = express(); // Create Express application

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configure better logging for improved debugging
const { Theme } = betterLogging;
betterLogging(console, {
  color: Theme.green, // Set console color theme
});

// Enable detailed debug output (level 4 includes debug messages)
console.logLevel = 4;

// Register a custom middleware for logging incoming requests
app.use(
  betterLogging.expressMiddleware(console, {
    ip: { show: true, color: Theme.green.base },
    method: { show: true, color: Theme.green.base },
    header: { show: false },
    path: { show: true },
    body: { show: true },
  })
);

// Configure session management with secure settings
const sessionConf = expressSession({
  secret: "booking-system-secret-key-2024", // Strong secret for session signing
  resave: true, // Forces session to be saved back to the store
  saveUninitialized: true, // Forces a session that is "uninitialized" to be saved
  cookie: {
    httpOnly: true, // Prevents client-side JS from reading the cookie
    secure: true, // Require HTTPS for cookies
    maxAge: 48 * 60 * 60 * 1000, // Cookie expires after 48 hours (extended as requested)
    sameSite: 'strict' // Prevent CSRF attacks
  },
});

// Configure security with Helmet
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"], // Allow inline scripts for Vue
      styleSrc: ["'self'", "'unsafe-inline'"], // Allow inline styles for Vue
      imgSrc: ["'self'", 'data:'], // Allow data URIs for images
      connectSrc: ["'self'", 'ws:', 'wss:'] // Allow WebSocket connections
    }
  },
  xssFilter: true,
  noSniff: true,
  referrerPolicy: { policy: 'same-origin' }
}));

// Configure CORS with secure settings
app.use(cors({
  origin: ['http://localhost:5173', 'https://localhost:5173', 'https://localhost:8989'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Apply session middleware to Express app
app.use(sessionConf);

// Apply custom security middleware
app.use(setSecurityHeaders);
app.use(escapeHtml);

// Serve static files from the client/dist directory (Vue.js frontend)
app.use(express.static(resolvePath(__dirname, "../../client/dist")));

// Register middlewares to parse request bodies with size limits
app.use(express.json({ limit: '1mb' })); // Parse JSON request bodies
app.use(express.urlencoded({ extended: true, limit: '1mb' })); // Parse URL-encoded request bodies

// Bind REST controllers to /api/* routes
app.use("/api", auth.router); // Authentication routes (login, logout)
app.use("/api", admin.router); // Admin routes (create/delete timeslots)
app.use("/api", auth.requireAuth, timeslot.router); // Timeslot routes (list, reserve, book) - protected
app.use("/api", auth.requireAuth, chat.router); // Chat routes (requires authentication)

// Catch-all route to serve the SPA for client-side routing
app.get("*", (req, res) => {
  res.sendFile(resolvePath(__dirname, "../../client/dist/index.html"));
});

/**
 * Initialize the application with HTTPS support
 */
async function init() {
  try {
    // Initialize the database
    await initDB();
    console.log("Database initialized successfully");

    // Get database connection for creating default data
    const db = getDB();

    // Check if we have any timeslots in the database
    const timeslotsCount = await db.get(
      "SELECT COUNT(*) as count FROM timeslots"
    );

    // If no timeslots exist, create some default ones for demonstration
    if (timeslotsCount.count === 0) {
      console.log("No timeslots found, creating default timeslots...");

      // Get all assistants from the database
      const assistants = await db.all("SELECT * FROM assistants");

      if (assistants.length > 0) {
        // Define standard time slots for lab sessions
        const timeSlots = [
          "09:00",
          "10:00", 
          "11:00",
          "13:00",
          "14:00",
          "15:00",
        ];

        // Get today's date in YYYY-MM-DD format for the default timeslots
        const today = new Date().toISOString().split("T")[0];

        // Create timeslots for each assistant at each time
        for (const assistant of assistants) {
          for (const time of timeSlots) {
            // Insert a new timeslot into the database
            await db.run(
              "INSERT INTO timeslots (assistant_id, date, time, location, booked, booked_by, reserved_until) VALUES (?, ?, ?, ?, 0, NULL, NULL)",
              [assistant.id, today, time, "Room A"]
            );
          }
        }

        console.log("Default timeslots created successfully");
      } else {
        console.log("No assistants found, cannot create timeslots");
      }
    } else {
      console.log(`Found ${timeslotsCount.count} existing timeslots`);
    }

    // Load SSL/TLS certificates
    const certificates = await loadCertificates();
    console.log("SSL/TLS certificates loaded");

    // Create HTTP and HTTPS servers
    const httpServer = createHttpServer(app);
    const httpsServer = createHttpsServer({
      key: certificates.key,
      cert: certificates.cert
    }, app);

    // Create Socket.io server attached to HTTPS server
    const io = new Server(httpsServer, {
      cors: {
        origin: ['http://localhost:5173', 'https://localhost:5173', 'https://localhost:8989'],
        credentials: true
      }
    });

    // Share the same session between Express and Socket.io
    io.use(
      socketIOSession(sessionConf, {
        autoSave: true,
        saveUninitialized: true,
      })
    );

    // Handle Socket.io connections for real-time communication
    io.on("connection", (socket) => {
      // Get the session associated with this socket connection
      const { session } = socket.handshake;

      // Store the socket ID in the session for later reference
      session.socketID = socket.id;

      // Save the updated session with the socket ID
      session.save((err) => {
        if (err) console.error(err);
        else console.debug(`Saved socketID: ${session.socketID}`);
      });

      // Update user activity timestamp if the user exists
      if (session.id) {
        const user = model.findUserById(session.id);
        if (user) {
          user.updateActivity();
          console.debug(`Updated activity for user: ${user.getName()}`);
        }
      }

      // Send current timeslots to the newly connected client
      const timeslots = model.getTimeslots().map((timeslot) => timeslot.toJSON());
      socket.emit("timeslots_init", { timeslots });

      // Handle user activity events from the client
      socket.on("user_activity", () => {
        if (session.id) {
          const user = model.findUserById(session.id);
          if (user) {
            user.updateActivity();
            console.debug(`Updated activity for user: ${user.getName()} via Socket.io`);
          }
        }
      });

      // Handle client disconnection
      socket.on("disconnect", () => {
        console.debug(`Client disconnected: ${socket.id}`);
      });
    });

    // Initialize the model with the Socket.io instance for real-time updates
    await model.init(io);

    // Set up a periodic cleanup of inactive users and their reservations
    const cleanupInterval = setInterval(async () => {
      try {
        await model.cleanupInactiveUsers(30 * 1000);
      } catch (error) {
        console.error("Error during inactive users cleanup:", error);
      }
    }, 10000);

    // Handle graceful shutdown
    const gracefulShutdown = async () => {
      console.log("Shutting down server...");
      clearInterval(cleanupInterval);
      
      httpsServer.close(() => {
        console.log("HTTPS server closed");
      });
      
      httpServer.close(() => {
        console.log("HTTP server closed");
        process.exit(0);
      });
    };

    // Listen for termination signals
    process.on("SIGTERM", gracefulShutdown);
    process.on("SIGINT", gracefulShutdown);

    // Configure HTTP server to redirect to HTTPS
    const httpApp = express();
    httpApp.use((req, res) => {
      const httpsUrl = `https://localhost:${httpsPort}${req.url}`;
      console.log(`Redirecting from HTTP to HTTPS: ${httpsUrl}`);
      res.redirect(301, httpsUrl);
    });

    // Start the HTTP server (for redirecting to HTTPS)
    const redirectServer = createHttpServer(httpApp);
    redirectServer.listen(httpPort, () => {
      console.log(`HTTP server listening on http://localhost:${httpPort}/ (redirects to HTTPS)`);
    });

    // Start the HTTPS server
    httpsServer.listen(httpsPort, () => {
      console.log(`HTTPS server listening on https://localhost:${httpsPort}/`);
    });

  } catch (error) {
    console.error("Error initializing application:", error);
    process.exit(1);
  }
}

// Start the application
init();
