/**
 * index.js - Main Server Entry Point
 */

import express from "express";
import cors from "cors";
import { createServer as createHttpsServer } from "https";
import { loadCertificates } from "./utils/certificate.js";

const app = express();
const httpsPort = 8989;

// Configure CORS
app.use(cors({
  origin: ['http://localhost:5173', 'https://localhost:5173', 'https://localhost:8989'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Parse JSON
app.use(express.json());

// Basic test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'Server is working!' });
});

// Start server
async function init() {
  try {
    console.log("Starting server...");
    
    // Load certificates
    const certificates = await loadCertificates();
    console.log("SSL/TLS certificates loaded");

    // Create HTTPS server
    const httpsServer = createHttpsServer({
      key: certificates.key,
      cert: certificates.cert
    }, app);

    // Start the HTTPS server
    httpsServer.listen(httpsPort, () => {
      console.log(`HTTPS server listening on https://localhost:${httpsPort}/`);
    });

  } catch (error) {
    console.error("Error initializing application:", error);
    process.exit(1);
  }
}

init();
